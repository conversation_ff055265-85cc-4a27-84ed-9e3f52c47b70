/**
 * TypeScript interfaces for Document Review Workflow
 *
 * These interfaces extend the existing contracts from @belbooks/types
 * to provide frontend-specific types for the document review process.
 */

import type {
  ExtractionResult,
  Suggestion,
  DocumentStatus,
  ConfirmRequest,
  ConfirmResponse,
  RegionView,
  RegionCandidate
} from '@belbooks/types'

// Re-export core types for convenience
export type { ExtractionResult, Suggestion, DocumentStatus, ConfirmRequest, ConfirmResponse, RegionView, RegionCandidate }

/**
 * Document data structure used in the inbox list
 */
export interface InboxDocument {
  id: number
  entity_id: number
  path: string
  file_hash: string
  mime_type: string
  source: 'upload' | 'email' | 'api'
  status: DocumentStatus
  extraction?: ExtractionResult | null
  suggestion?: Suggestion | null
  confidence?: number | null
  posted_journal_id?: number | null
  export_ref?: string | null
  error_msg?: string | null
  confirmed_at?: string | null
  confirmed_by?: string | null
  created_at: string
  updated_at: string
  // Additional fields that may be populated from extraction
  supplier_name?: string | null
  invoice_number?: string | null
  invoice_date?: string | null
  gross_amount?: string | null
  vat_amount?: string | null
  net_amount?: string | null
}

/**
 * Document review modal state
 */
export interface DocumentReviewState {
  isOpen: boolean
  document: InboxDocument | null
  loading: boolean
  error: string | null
  submitting: boolean
}

/**
 * Form data for document review with editable fields
 */
export interface DocumentReviewFormData {
  // Supplier information
  supplierName: string
  supplierVat: string
  supplierAddress: string

  // Invoice information
  invoiceNumber: string
  invoiceIssueDate: string
  invoiceDueDate: string
  invoiceCurrency: 'EUR'
  invoiceNet: string
  invoiceVat: string
  invoiceGross: string

  // Payment instructions
  paymentIban: string
  paymentBic: string
  paymentStructuredRef: string

  // Journal information (from suggestion)
  journalDate: string
  journalReference: string
  journalDescription: string

  // Line items (if available)
  lines: Array<{
    description: string
    quantity: string
    unitPrice: string
    vatRate: 0 | 21 | 12 | 6
    accountHint?: number
  }>

  // Suggestion lines for review
  suggestionLines: Array<{
    accountId: number
    debit: string
    credit: string
    vatCodeId?: number
    memo?: string
  }>
}

/**
 * Validation errors for the review form
 */
export interface DocumentReviewFormErrors {
  supplierName?: string
  supplierVat?: string
  supplierAddress?: string
  invoiceNumber?: string
  invoiceIssueDate?: string
  invoiceDueDate?: string
  invoiceCurrency?: string
  invoiceNet?: string
  invoiceVat?: string
  invoiceGross?: string
  paymentIban?: string
  paymentBic?: string
  paymentStructuredRef?: string
  journalDate?: string
  journalReference?: string
  journalDescription?: string
  lines?: Array<{
    description?: string
    quantity?: string
    unitPrice?: string
    vatRate?: string
  }>
  suggestionLines?: Array<{
    accountId?: string
    debit?: string
    credit?: string
  }>
  general?: string
}

/**
 * Document review action types
 */
export type DocumentReviewAction =
  | 'view'      // View document (existing functionality)
  | 'review'    // Review and potentially confirm document
  | 'process'   // Process unprocessed document
  | 'delete'    // Delete document

/**
 * Document review modal props
 */
export interface DocumentReviewModalProps {
  isOpen: boolean
  document: InboxDocument | null
  onClose: () => void
  onConfirm: (documentId: number, corrections?: Partial<ExtractionResult>) => Promise<void>
  loading?: boolean
  error?: string | null
}

/**
 * Document action button props
 */
export interface DocumentActionButtonProps {
  document: InboxDocument
  action: DocumentReviewAction
  onClick: () => void
  loading?: boolean
  disabled?: boolean
  className?: string
}

/**
 * Helper type for document status checks
 */
export interface DocumentStatusHelpers {
  isUnprocessed: (status: DocumentStatus) => boolean
  requiresReview: (status: DocumentStatus) => boolean
  isReadyForReview: (status: DocumentStatus) => boolean
  isProcessed: (status: DocumentStatus) => boolean
  canBeReviewed: (status: DocumentStatus) => boolean
  canBeProcessed: (status: DocumentStatus) => boolean
}

/**
 * API response wrapper for document operations
 */
export interface DocumentApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

/**
 * Document confirmation request with optional corrections
 */
export interface DocumentConfirmationRequest {
  documentId: number
  corrections?: Partial<ExtractionResult>
}

/**
 * Document review workflow hooks return type
 */
export interface UseDocumentReviewReturn {
  // State
  reviewState: DocumentReviewState
  formData: DocumentReviewFormData
  formErrors: DocumentReviewFormErrors

  // Actions
  openReview: (document: InboxDocument) => void
  closeReview: () => void
  updateFormData: (updates: Partial<DocumentReviewFormData>) => void
  confirmDocument: (corrections?: Partial<ExtractionResult>) => Promise<void>

  // Helpers
  validateForm: () => boolean
  resetForm: () => void
  hasChanges: () => boolean
}

/**
 * Constants for document review workflow
 */
export const DOCUMENT_REVIEW_CONSTANTS = {
  STATUSES: {
    UPLOADED: 'uploaded' as const,
    EXTRACTED: 'extracted' as const,
    SUGGESTED: 'suggested' as const,
    CONFIRMED: 'confirmed' as const,
    POSTED: 'posted' as const,
    EXPORTED: 'exported' as const,
    FAILED: 'failed' as const,
  },
  ACTIONS: {
    VIEW: 'view' as const,
    REVIEW: 'review' as const,
    PROCESS: 'process' as const,
    DELETE: 'delete' as const,
  },
  CURRENCIES: {
    EUR: 'EUR' as const,
  },
  VAT_RATES: {
    STANDARD: 21,
    REDUCED: 12,
    LOW: 6,
    ZERO: 0,
  }
} as const

/**
 * Document status helper functions
 */
export const documentStatusHelpers: DocumentStatusHelpers = {
  isUnprocessed: (status: DocumentStatus) => status === 'uploaded',
  requiresReview: (status: DocumentStatus) => status === 'extracted',
  isReadyForReview: (status: DocumentStatus) => status === 'suggested',
  isProcessed: (status: DocumentStatus) => ['confirmed', 'posted', 'exported'].includes(status),
  canBeReviewed: (status: DocumentStatus) => ['extracted', 'suggested'].includes(status),
  canBeProcessed: (status: DocumentStatus) => status === 'uploaded',
}
